<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.squirrel.dao.NoticeMapper" >
    <resultMap id="BaseResultMap" type="com.squirrel.pojo.Notice" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="create_at" property="createAt" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.squirrel.pojo.Notice" extends="BaseResultMap" >
        <result column="context" property="context" jdbcType="LONGVARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, user_id, create_at, status
    </sql>
    <sql id="Blob_Column_List" >
        context
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from t_shop_notice
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from t_shop_notice
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.squirrel.pojo.Notice" >
        insert into t_shop_notice (id, user_id, create_at,
        status, context)
        values (#{id,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{createAt,jdbcType=VARCHAR},
        #{status,jdbcType=TINYINT}, #{context,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.squirrel.pojo.Notice" >
        insert into t_shop_notice
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="createAt != null" >
                create_at,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="context != null" >
                context,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="createAt != null" >
                #{createAt,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="context != null" >
                #{context,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.squirrel.pojo.Notice" >
        update t_shop_notice
        <set >
            <if test="userId != null" >
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="createAt != null" >
                create_at = #{createAt,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="context != null" >
                context = #{context,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.squirrel.pojo.Notice" >
        update t_shop_notice
        set user_id = #{userId,jdbcType=INTEGER},
        create_at = #{createAt,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        context = #{context,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.squirrel.pojo.Notice" >
        update t_shop_notice
        set user_id = #{userId,jdbcType=INTEGER},
        create_at = #{createAt,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>