# 校园二手交易平台

## **项目介绍**
##### **项目原型:** [squirrel](https://github.com/hlk-1135/squirrel)
##### **项目原型的技术选型:**
- Spring+SpringMVC+MyBatis
- JavaScript+Jquery+React

## **技术选型:**
- [JDK1.8]()
- [Maven]()
- [SpringBoot](https://projects.spring.io/spring-boot/)
- [MyBatis](http://www.mybatis.org/mybatis-3/zh/index.html)
- [Thymeleaf](http://www.thymeleaf.org/doc/tutorials/3.0/thymeleafspring.html)
- [MySQL]()
- [JQuery]()
- [Bootstrap]()
- [FontAwesome]()
- [Html+CSS+JavaScript]()
 
## **开发环境:** 
JDK1.8, Maven 3.3.9, IntelliJ IDEA, Navicat Premium, Git

## **效果预览**
![首页](https://github.com/gdufeZLYL/blog/blob/master/images/20180421162953.png)

![登录](https://github.com/gdufeZLYL/blog/blob/master/images/20180421165357.png)

![商品详情页](https://github.com/gdufeZLYL/blog/blob/master/images/20180421165448.png)

![评论](https://github.com/gdufeZLYL/blog/blob/master/images/20180421165626.png)

## **讨论**
有问题请在[issue](https://github.com/gdufeZLYL/springboot-squirrel/issues)讨论