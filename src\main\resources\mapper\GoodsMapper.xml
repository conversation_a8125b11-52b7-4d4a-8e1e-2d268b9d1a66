<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.squirrel.dao.GoodsMapper" >
    <resultMap id="BaseResultMap" type="com.squirrel.pojo.Goods" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="catelog_id" property="catelogId" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="price" property="price" jdbcType="REAL" />
        <result column="real_price" property="realPrice" jdbcType="REAL" />
        <result column="start_time" property="startTime" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="VARCHAR" />
        <result column="commet_num" property="commetNum" jdbcType="INTEGER" />
        <result column="polish_time" property="polishTime" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.squirrel.pojo.Goods" extends="BaseResultMap" >
        <result column="describle" property="describle" jdbcType="LONGVARCHAR" />
        <result column="state" property="state" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, catelog_id, user_id, name, price, real_price, start_time, polish_time, end_time, commet_num
    </sql>
    <sql id="Blob_Column_List" >
        describle,state
    </sql>
    <sql id="Factor">
        <!-- TODO::会引起分页插件bug -->
        --     and end_time >= now() order by polish_time DESC
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select
        *
        from t_shop_goods
        where id = #{id,jdbcType=INTEGER}
        <include refid="Factor"></include>
    </select>
    <select id="searchGoods" resultMap="ResultMapWithBLOBs" parameterType="String" >
        select
        *
        from t_shop_goods
        where name like concat('%',#{name},'%') OR describle like concat('%',#{describle},'%')
        <include refid="Factor"></include>
    </select>

    <select id="selectCountByCatelog" resultType="int">
        select COUNT(1) from t_shop_goods
        where 1 = 1
        <if test="catelog_id != 0">
            AND catelog_id = #{catelog_id,jdbcType=INTEGER}
        </if>
        <if test="name != null and name != ''" >
            AND (name like CONCAT(CONCAT('%', #{name}), '%')
        </if>
        <if test="describle != null and describle != ''" >
            OR describle like CONCAT(CONCAT('%', #{describle}), '%'))
        </if>
        <include refid="Factor"></include>
    </select>

    <select id="selectByCatelog" resultMap="ResultMapWithBLOBs">
        select * from t_shop_goods
        where 1 = 1
        <if test="catelog_id != 0">
            AND catelog_id = #{catelog_id,jdbcType=INTEGER}
        </if>
        <if test="name != null and name != ''" >
            AND (name LIKE CONCAT(CONCAT('%', #{name}), '%')
        </if>
        <if test="describle != null and describle != ''" >
            OR describle LIKE CONCAT(CONCAT('%', #{describle}), '%'))
        </if>
    </select>

    <select id="selectAllGoods" resultMap="ResultMapWithBLOBs">
        select * from t_shop_goods
    </select>
    <select id="getGoodsByUserId" resultMap="ResultMapWithBLOBs">
        select * from t_shop_goods where user_id = #{user_id,jdbcType=INTEGER}
    </select>

    <select id="selectByCatelogOrderByDate" resultMap="ResultMapWithBLOBs">
        select * from t_shop_goods
        where catelog_id = #{catelogId,jdbcType=INTEGER}
        order by polish_time DESC
        limit #{limit,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from t_shop_goods
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.squirrel.pojo.Goods" useGeneratedKeys="true" keyProperty="id">
        insert into t_shop_goods (id, catelog_id, user_id,
        name, price, real_price, start_time,
        end_time, polish_time, commet_num, describle)
        values (#{id,jdbcType=INTEGER}, #{catelogId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER},
        #{name,jdbcType=VARCHAR}, #{price,jdbcType=REAL}, #{realPrice,jdbcType=REAL}, #{startTime,jdbcType=VARCHAR},
        #{endTime,jdbcType=VARCHAR}, #{polishTime,jdbcType=VARCHAR}, #{commetNum,jdbcType=INTEGER}, #{describle,jdbcType=LONGVARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.squirrel.pojo.Goods" >
        insert into t_shop_goods
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="catelogId != null" >
                catelog_id,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="price != null" >
                price,
            </if>
            <if test="realPrice != null" >
                real_price,
            </if>
            <if test="startTime != null" >
                start_time,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="polishTime != null" >
                polish_time,
            </if>
            <if test="commetNum != null" >
                commet_num,
            </if>
            <if test="describle != null" >
                describle,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="catelogId != null" >
                #{catelogId,jdbcType=INTEGER},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="price != null" >
                #{price,jdbcType=REAL},
            </if>
            <if test="realPrice != null" >
                #{realPrice,jdbcType=REAL},
            </if>
            <if test="startTime != null" >
                #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                #{polishTime,jdbcType=VARCHAR},
            </if>
            <if test="commetNum != null" >
                #{commetNum,jdbcType=INTEGER},
            </if>
            <if test="describle != null" >
                #{describle,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.squirrel.pojo.Goods" >
        update t_shop_goods
        <set >
            <if test="catelogId != null" >
                catelog_id = #{catelogId,jdbcType=INTEGER},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="name != null" >
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="price != null" >
                price = #{price,jdbcType=REAL},
            </if>
            <if test="realPrice != null" >
                real_price = #{realPrice,jdbcType=REAL},
            </if>
            <if test="startTime != null" >
                start_time = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                polish_time = #{polishTime,jdbcType=VARCHAR},
            </if>
            <if test="commetNum != null" >
                commet_num = #{commetNum,jdbcType=INTEGER},
            </if>
            <if test="describle != null" >
                describle = #{describle,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.squirrel.pojo.Goods" >
        update t_shop_goods
        set catelog_id = #{catelogId,jdbcType=INTEGER},
        user_id = #{userId,jdbcType=INTEGER},
        name = #{name,jdbcType=VARCHAR},
        price = #{price,jdbcType=REAL},
        real_price = #{realPrice,jdbcType=REAL},
        start_time = #{startTime,jdbcType=VARCHAR},
        end_time = #{endTime,jdbcType=VARCHAR},
        polish_time = #{polishTime,jdbcType=VARCHAR},
        commet_num = #{commetNum,jdbcType=INTEGER},
        describle = #{describle,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.squirrel.pojo.Goods" >
        update t_shop_goods
        set catelog_id = #{catelogId,jdbcType=INTEGER},
        user_id = #{userId,jdbcType=INTEGER},
        name = #{name,jdbcType=VARCHAR},
        price = #{price,jdbcType=REAL},
        real_price = #{realPrice,jdbcType=REAL},
        start_time = #{startTime,jdbcType=VARCHAR},
        end_time = #{endTime,jdbcType=VARCHAR},
        polish_time = #{polishTime,jdbcType=VARCHAR},
        commet_num = #{commetNum,jdbcType=INTEGER},
        describle = #{describle,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>