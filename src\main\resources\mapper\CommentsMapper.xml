<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.squirrel.dao.CommentsMapper" >
    <resultMap id="BaseResultMap" type="com.squirrel.pojo.Comments" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="atuser_id" property="atuserId" jdbcType="INTEGER" />
        <result column="goods_id" property="goodsId" jdbcType="INTEGER" />
        <result column="create_at" property="createAt" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.squirrel.pojo.Comments" extends="BaseResultMap" >
        <result column="content" property="content" jdbcType="LONGVARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, user_id, atuser_id goods_id, create_at
    </sql>
    <sql id="Blob_Column_List" >
        content
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from t_shop_comments
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from t_shop_comments
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.squirrel.pojo.Comments" >
        insert into t_shop_comments (user_id, atuser_id, goods_id,
        create_at, content)
        values (#{userId,jdbcType=INTEGER},
        #{atuserId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER},
        #{createAt,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.squirrel.pojo.Comments" >
        insert into t_shop_comments
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="atuserId != null" >
                atuser_id,
            </if>
            <if test="goodsId != null" >
                goods_id,
            </if>
            <if test="createAt != null" >
                create_at,
            </if>
            <if test="content != null" >
                content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="atuserId != null" >
                #{atuserId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null" >
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="createAt != null" >
                #{createAt,jdbcType=VARCHAR},
            </if>
            <if test="content != null" >
                #{content,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.squirrel.pojo.Comments" >
        update t_shop_comments
        <set >
            <if test="userId != null" >
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="atuserId != null" >
                atuser_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null" >
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="createAt != null" >
                create_at = #{createAt,jdbcType=VARCHAR},
            </if>
            <if test="content != null" >
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.squirrel.pojo.Comments" >
        update t_shop_comments
        set user_id = #{userId,jdbcType=INTEGER},
        atuser_id = #{atuserId,jdbcType=INTEGER},
        goods_id = #{goodsId,jdbcType=INTEGER},
        create_at = #{createAt,jdbcType=VARCHAR},
        content = #{content,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.squirrel.pojo.Comments" >
        update t_shop_comments
        set user_id = #{userId,jdbcType=INTEGER},
        atuser_id = #{atuserId,jdbcType=INTEGER},
        goods_id = #{goodsId,jdbcType=INTEGER},
        create_at = #{createAt,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByGoodsId" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select
        *
        from t_shop_comments
        where goods_id = #{goodsId,jdbcType=INTEGER}
    </select>
</mapper>