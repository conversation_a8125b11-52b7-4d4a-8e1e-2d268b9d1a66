package com.squirrel.pojo;

public class Catelog {
    private Integer id;

    private String name;

    private Integer number;

    private Byte status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "Catelog{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", number=" + number +
                ", status=" + status +
                '}';
    }
}
