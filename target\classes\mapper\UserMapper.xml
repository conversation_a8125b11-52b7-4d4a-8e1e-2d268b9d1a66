<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.squirrel.dao.UserMapper" >
    <resultMap id="BaseResultMap" type="com.squirrel.pojo.User" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="phone" property="phone" jdbcType="CHAR" />
        <result column="username" property="username" jdbcType="VARCHAR" />
        <result column="password" property="password" jdbcType="CHAR" />
        <result column="QQ" property="qq" jdbcType="VARCHAR" />
        <result column="create_at" property="createAt" jdbcType="VARCHAR" />
        <result column="goods_num" property="goodsNum" jdbcType="INTEGER" />
        <result column="power" property="power" jdbcType="TINYINT" />
        <result column="last_login" property="lastLogin" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, phone, username, password, QQ, create_at, goods_num, power, last_login, status
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from t_shop_user
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getUserList" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" /> from t_shop_user where 1=1
        ORDER BY create_at DESC
        <!--<if test="username != null">-->
        <!--<![CDATA[-->
        <!--and username like CONCAT('%',#{username,jdbcType=VARCHAR}, '%')-->
        <!--]]>-->
        <!--</if>-->
    </select>

    <select id="getCount" resultType="int">
        select
          COUNT(1)
        from t_shop_user
    </select>

    <!--根据手机号查询用户-->
    <select id="getUserByPhone" resultMap="BaseResultMap" parameterType="String" >
        select
        <include refid="Base_Column_List" />
        from t_shop_user
        where phone = #{phone,jdbcType=CHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from t_shop_user
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.squirrel.pojo.User" >
        insert into t_shop_user (id, phone, username,
        password, QQ, create_at,
        goods_num, power, last_login,
        status)
        values (#{id,jdbcType=INTEGER}, #{phone,jdbcType=CHAR}, #{username,jdbcType=VARCHAR},
        #{password,jdbcType=CHAR}, #{qq,jdbcType=VARCHAR}, #{createAt,jdbcType=VARCHAR},
        #{goodsNum,jdbcType=INTEGER}, #{power,jdbcType=TINYINT}, #{lastLogin,jdbcType=VARCHAR},
        #{status,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="com.squirrel.pojo.User" >
        insert into t_shop_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="phone != null" >
                phone,
            </if>
            <if test="username != null" >
                username,
            </if>
            <if test="password != null" >
                password,
            </if>
            <if test="qq != null" >
                QQ,
            </if>
            <if test="createAt != null" >
                create_at,
            </if>
            <if test="goodsNum != null" >
                goods_num,
            </if>
            <if test="power != null" >
                power,
            </if>
            <if test="lastLogin != null" >
                last_login,
            </if>
            <if test="status != null" >
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="phone != null" >
                #{phone,jdbcType=CHAR},
            </if>
            <if test="username != null" >
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null" >
                #{password,jdbcType=CHAR},
            </if>
            <if test="qq != null" >
                #{qq,jdbcType=VARCHAR},
            </if>
            <if test="createAt != null" >
                #{createAt,jdbcType=VARCHAR},
            </if>
            <if test="goodsNum != null" >
                #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="power != null" >
                #{power,jdbcType=TINYINT},
            </if>
            <if test="lastLogin != null" >
                #{lastLogin,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.squirrel.pojo.User" >
        update t_shop_user
        <set >
            <if test="phone != null" >
                phone = #{phone,jdbcType=CHAR},
            </if>
            <if test="username != null" >
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null" >
                password = #{password,jdbcType=CHAR},
            </if>
            <if test="qq != null" >
                QQ = #{qq,jdbcType=VARCHAR},
            </if>
            <if test="createAt != null" >
                create_at = #{createAt,jdbcType=VARCHAR},
            </if>
            <if test="goodsNum != null" >
                goods_num = #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="power != null" >
                power = #{power,jdbcType=TINYINT},
            </if>
            <if test="lastLogin != null" >
                last_login = #{lastLogin,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.squirrel.pojo.User" >
        update t_shop_user
        set phone = #{phone,jdbcType=CHAR},
        username = #{username,jdbcType=VARCHAR},
        password = #{password,jdbcType=CHAR},
        QQ = #{qq,jdbcType=VARCHAR},
        create_at = #{createAt,jdbcType=VARCHAR},
        goods_num = #{goodsNum,jdbcType=INTEGER},
        power = #{power,jdbcType=TINYINT},
        last_login = #{lastLogin,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateGoodsNum" parameterType="Integer" >
        update t_shop_user set
        goods_num = #{goodsNum,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateStatus" >
        update t_shop_user set
        status = #{status}
        where id = #{id}
    </update>

    <select id="getUsersByIds" resultMap="BaseResultMap">
        SELECT
          *
        FROM t_shop_user
        WHERE 1 = 1
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getUsersByIdsSet" resultMap="BaseResultMap">
        SELECT
        *
        FROM t_shop_user
        WHERE 1 = 1
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>