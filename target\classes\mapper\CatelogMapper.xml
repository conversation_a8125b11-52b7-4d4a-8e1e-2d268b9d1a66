<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.squirrel.dao.CatelogMapper" >
    <resultMap id="BaseResultMap" type="com.squirrel.pojo.Catelog" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="number" property="number" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="TINYINT" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, name, number, status
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from t_shop_catelog
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getAllCatelog" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_shop_catelog
        where status = 1
        ORDER BY id ASC
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from t_shop_catelog
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.squirrel.pojo.Catelog" >
        insert into t_shop_catelog (id, name, number,
        status)
        values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{number,jdbcType=INTEGER},
        #{status,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="com.squirrel.pojo.Catelog" >
        insert into t_shop_catelog
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="number != null" >
                number,
            </if>
            <if test="status != null" >
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="number != null" >
                #{number,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.squirrel.pojo.Catelog" >
        update t_shop_catelog
        <set >
            <if test="name != null" >
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="number != null" >
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.squirrel.pojo.Catelog" >
        update t_shop_catelog
        set name = #{name,jdbcType=VARCHAR},
        number = #{number,jdbcType=INTEGER},
        status = #{status,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateCatelogNum" parameterType="Integer" >
        update t_shop_catelog
        set number = #{number,jdbcType=INTEGER} where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>