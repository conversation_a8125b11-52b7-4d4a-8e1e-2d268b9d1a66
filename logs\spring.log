2025-09-09 21:19:41.488  INFO 33552 --- [main] com.squirrel.Application                 : Starting Application on LC-远海 with PID 33552 (E:\Documents\springboot-squirrel-master\springboot-squirrel-master\target\classes started by <PERSON> in E:\Documents\springboot-squirrel-master\springboot-squirrel-master)
2025-09-09 21:19:41.490  INFO 33552 --- [main] com.squirrel.Application                 : No active profile set, falling back to default profiles: default
2025-09-09 21:19:41.638  INFO 33552 --- [main] ationConfigEmbeddedWebApplicationContext : Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@568bf312: startup date [Tue Sep 09 21:19:41 CST 2025]; root of context hierarchy
2025-09-09 21:19:41.930  INFO 33552 --- [main] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'dataSource' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=druidConfig; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/squirrel/config/DruidConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=application; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=close; defined in com.squirrel.Application]
2025-09-09 21:19:42.446  INFO 33552 --- [main] s.b.c.e.t.TomcatEmbeddedServletContainer : Tomcat initialized with port(s): 8081 (http)
2025-09-09 21:19:42.452  INFO 33552 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-09 21:19:42.453  INFO 33552 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet Engine: Apache Tomcat/8.5.23
2025-09-09 21:19:42.580  INFO 33552 --- [localhost-startStop-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-09 21:19:42.580  INFO 33552 --- [localhost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 942 ms
2025-09-09 21:19:42.733  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'statViewServlet' to [/druid/*]
2025-09-09 21:19:42.734  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'dispatcherServlet' to [/]
2025-09-09 21:19:42.736  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-09 21:19:42.736  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-09 21:19:42.736  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-09-09 21:19:42.736  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'requestContextFilter' to: [/*]
2025-09-09 21:19:42.736  INFO 33552 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'webStatFilter' to urls: [/*]
2025-09-09 21:19:43.207  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@568bf312: startup date [Tue Sep 09 21:19:41 CST 2025]; root of context hierarchy
2025-09-09 21:19:43.243  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/deleteComments/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.deleteComments(int)
2025-09-09 21:19:43.244  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/updateComments],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.updateComments(com.squirrel.pojo.Comments)
2025-09-09 21:19:43.244  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/addComments],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.addaddComments(com.squirrel.pojo.Comments)
2025-09-09 21:19:43.248  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/publishGoodsSubmit]}" onto public java.lang.String com.squirrel.controller.GoodsController.publishGoodsSubmit(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.Image,com.squirrel.pojo.Goods,org.springframework.web.multipart.MultipartFile) throws java.lang.Exception
2025-09-09 21:19:43.248  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/api/offGoods/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.GoodsController.offGoods(int)
2025-09-09 21:19:43.248  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/offGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.offGoods() throws java.lang.Exception
2025-09-09 21:19:43.248  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/deleteGoods/{id}]}" onto public java.lang.String com.squirrel.controller.GoodsController.deleteGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer) throws java.lang.Exception
2025-09-09 21:19:43.248  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/goodsId/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.getGoodsById(javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.String) throws java.lang.Exception
2025-09-09 21:19:43.248  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/search]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.searchGoods(javax.servlet.http.HttpServletRequest,java.lang.String) throws java.lang.Exception
2025-09-09 21:19:43.249  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/catelog/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.catelogGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.String) throws java.lang.Exception
2025-09-09 21:19:43.249  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/homeGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.homeGoods(javax.servlet.http.HttpServletRequest) throws java.lang.Exception
2025-09-09 21:19:43.249  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/editGoods/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.editGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer) throws java.lang.Exception
2025-09-09 21:19:43.249  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/editGoodsSubmit]}" onto public java.lang.String com.squirrel.controller.GoodsController.editGoodsSubmit(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.Goods) throws java.lang.Exception
2025-09-09 21:19:43.249  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/publishGoods]}" onto public java.lang.String com.squirrel.controller.GoodsController.publishGoods(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:19:43.250  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/uploadFile]}" onto public java.util.Map<java.lang.String, java.lang.Object> com.squirrel.controller.GoodsController.uploadFile(javax.servlet.http.HttpSession,org.springframework.web.multipart.MultipartFile) throws java.lang.IllegalStateException,java.io.IOException
2025-09-09 21:19:43.250  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/api/updateGoods],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.GoodsController.updateGoods(com.squirrel.pojo.Goods)
2025-09-09 21:19:43.250  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/api/v1/users]}" onto public com.squirrel.util.UserGrid com.squirrel.controller.MainController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String)
2025-09-09 21:19:43.252  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/user/list],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.userList(javax.servlet.http.HttpServletRequest,int,org.springframework.ui.Model)
2025-09-09 21:19:43.252  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/goods/list],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.goodsList(javax.servlet.http.HttpServletRequest,int,int,java.lang.String,org.springframework.ui.Model)
2025-09-09 21:19:43.252  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/login],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.login(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:19:43.254  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/basic]}" onto public java.lang.String com.squirrel.controller.UserController.basic(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:19:43.254  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/changeName]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.changeName(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:19:43.254  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/home]}" onto public java.lang.String com.squirrel.controller.UserController.home(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:19:43.255  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/addUser]}" onto public java.lang.String com.squirrel.controller.UserController.addUser(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User)
2025-09-09 21:19:43.255  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/addUser],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.addUser(com.squirrel.pojo.User)
2025-09-09 21:19:43.255  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/allGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.goods(javax.servlet.http.HttpServletRequest)
2025-09-09 21:19:43.255  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/login],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.login(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-09-09 21:19:43.255  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/deleteUser/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.deleteUser(int)
2025-09-09 21:19:43.255  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/freezeUser/{id}],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.freezeUser(int)
2025-09-09 21:19:43.256  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/logout]}" onto public java.lang.String com.squirrel.controller.UserController.logout(javax.servlet.http.HttpServletRequest)
2025-09-09 21:19:43.256  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/login]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.loginValidate(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:19:43.256  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/unfreezeUser/{id}],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.unfreezeUser(int)
2025-09-09 21:19:43.256  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/updateUser],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.updateUser(com.squirrel.pojo.User)
2025-09-09 21:19:43.256  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/updateInfo]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.updateInfo(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:19:43.257  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-09-09 21:19:43.258  INFO 33552 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-09-09 21:19:43.280  INFO 33552 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/upload/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:19:43.282  INFO 33552 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:19:43.282  INFO 33552 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:19:43.310  INFO 33552 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:19:43.668  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Registering beans for JMX exposure on startup
2025-09-09 21:19:43.669  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'dataSource' has been autodetected for JMX exposure
2025-09-09 21:19:43.669  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'statFilter' has been autodetected for JMX exposure
2025-09-09 21:19:43.669  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'wallFilter' has been autodetected for JMX exposure
2025-09-09 21:19:43.673  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.pool:name=dataSource,type=DruidDataSource]
2025-09-09 21:19:43.674  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'wallFilter': registering with JMX server as MBean [com.alibaba.druid.wall:name=wallFilter,type=WallFilter]
2025-09-09 21:19:43.675  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
2025-09-09 21:19:43.691 ERROR 33552 --- [main] o.a.coyote.http11.Http11NioProtocol      : Failed to start end point associated with ProtocolHandler ["http-nio-8081"]

java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[na:1.8.0_411]
	at sun.nio.ch.Net.bind(Net.java:438) ~[na:1.8.0_411]
	at sun.nio.ch.Net.bind(Net.java:430) ~[na:1.8.0_411]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:225) ~[na:1.8.0_411]
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:74) ~[na:1.8.0_411]
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:210) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:990) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:635) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1022) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:150) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:225) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainer.addPreviouslyRemovedConnectors(TomcatEmbeddedServletContainer.java:250) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainer.start(TomcatEmbeddedServletContainer.java:193) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.startEmbeddedServletContainer(EmbeddedWebApplicationContext.java:297) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.finishRefresh(EmbeddedWebApplicationContext.java:145) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546) [spring-context-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at com.squirrel.Application.main(Application.java:55) [classes/:na]

2025-09-09 21:19:43.692 ERROR 33552 --- [main] o.apache.catalina.core.StandardService   : Failed to start connector [Connector[HTTP/1.1-8081]]

org.apache.catalina.LifecycleException: Failed to start component [Connector[HTTP/1.1-8081]]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:167) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:225) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainer.addPreviouslyRemovedConnectors(TomcatEmbeddedServletContainer.java:250) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainer.start(TomcatEmbeddedServletContainer.java:193) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.startEmbeddedServletContainer(EmbeddedWebApplicationContext.java:297) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.finishRefresh(EmbeddedWebApplicationContext.java:145) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546) [spring-context-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107) [spring-boot-1.5.9.RELEASE.jar:1.5.9.RELEASE]
	at com.squirrel.Application.main(Application.java:55) [classes/:na]
Caused by: org.apache.catalina.LifecycleException: service.getName(): "Tomcat";  Protocol handler start failed
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1031) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:150) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	... 13 common frames omitted
Caused by: java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[na:1.8.0_411]
	at sun.nio.ch.Net.bind(Net.java:438) ~[na:1.8.0_411]
	at sun.nio.ch.Net.bind(Net.java:430) ~[na:1.8.0_411]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:225) ~[na:1.8.0_411]
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:74) ~[na:1.8.0_411]
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:210) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:990) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:635) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1022) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	... 14 common frames omitted

2025-09-09 21:19:43.697  INFO 33552 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-09-09 21:19:43.712  INFO 33552 --- [main] utoConfigurationReportLoggingInitializer : 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
2025-09-09 21:19:43.722 ERROR 33552 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

The Tomcat connector configured to listen on port 8081 failed to start. The port may already be in use or the connector may be misconfigured.

Action:

Verify the connector's configuration, identify and stop any process that's listening on port 8081, or configure this application to listen on another port.

2025-09-09 21:19:43.722  INFO 33552 --- [main] ationConfigEmbeddedWebApplicationContext : Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@568bf312: startup date [Tue Sep 09 21:19:41 CST 2025]; root of context hierarchy
2025-09-09 21:19:43.724  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Unregistering JMX-exposed beans on shutdown
2025-09-09 21:19:43.724  INFO 33552 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Unregistering JMX-exposed beans
2025-09-09 21:21:20.109  INFO 20100 --- [main] com.squirrel.Application                 : Starting Application on LC-远海 with PID 20100 (E:\Documents\springboot-squirrel-master\springboot-squirrel-master\target\classes started by Bart in E:\Documents\springboot-squirrel-master\springboot-squirrel-master)
2025-09-09 21:21:20.110  INFO 20100 --- [main] com.squirrel.Application                 : No active profile set, falling back to default profiles: default
2025-09-09 21:21:20.225  INFO 20100 --- [main] ationConfigEmbeddedWebApplicationContext : Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@3bbc39f8: startup date [Tue Sep 09 21:21:20 CST 2025]; root of context hierarchy
2025-09-09 21:21:20.480  INFO 20100 --- [main] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'dataSource' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=druidConfig; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/squirrel/config/DruidConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=application; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=close; defined in com.squirrel.Application]
2025-09-09 21:21:20.952  INFO 20100 --- [main] s.b.c.e.t.TomcatEmbeddedServletContainer : Tomcat initialized with port(s): 8080 (http)
2025-09-09 21:21:20.957  INFO 20100 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-09 21:21:20.957  INFO 20100 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet Engine: Apache Tomcat/8.5.23
2025-09-09 21:21:21.073  INFO 20100 --- [localhost-startStop-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-09 21:21:21.073  INFO 20100 --- [localhost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 848 ms
2025-09-09 21:21:21.213  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'statViewServlet' to [/druid/*]
2025-09-09 21:21:21.214  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'dispatcherServlet' to [/]
2025-09-09 21:21:21.216  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-09 21:21:21.216  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-09 21:21:21.216  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-09-09 21:21:21.216  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'requestContextFilter' to: [/*]
2025-09-09 21:21:21.216  INFO 20100 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'webStatFilter' to urls: [/*]
2025-09-09 21:21:21.663  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@3bbc39f8: startup date [Tue Sep 09 21:21:20 CST 2025]; root of context hierarchy
2025-09-09 21:21:21.698  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/addComments],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.addaddComments(com.squirrel.pojo.Comments)
2025-09-09 21:21:21.699  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/deleteComments/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.deleteComments(int)
2025-09-09 21:21:21.699  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/updateComments],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.updateComments(com.squirrel.pojo.Comments)
2025-09-09 21:21:21.701  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/editGoods/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.editGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer) throws java.lang.Exception
2025-09-09 21:21:21.701  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/api/updateGoods],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.GoodsController.updateGoods(com.squirrel.pojo.Goods)
2025-09-09 21:21:21.701  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/search]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.searchGoods(javax.servlet.http.HttpServletRequest,java.lang.String) throws java.lang.Exception
2025-09-09 21:21:21.702  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/api/offGoods/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.GoodsController.offGoods(int)
2025-09-09 21:21:21.702  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/offGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.offGoods() throws java.lang.Exception
2025-09-09 21:21:21.702  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/homeGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.homeGoods(javax.servlet.http.HttpServletRequest) throws java.lang.Exception
2025-09-09 21:21:21.702  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/goodsId/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.getGoodsById(javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.String) throws java.lang.Exception
2025-09-09 21:21:21.702  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/deleteGoods/{id}]}" onto public java.lang.String com.squirrel.controller.GoodsController.deleteGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer) throws java.lang.Exception
2025-09-09 21:21:21.702  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/publishGoods]}" onto public java.lang.String com.squirrel.controller.GoodsController.publishGoods(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:21:21.703  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/catelog/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.catelogGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.String) throws java.lang.Exception
2025-09-09 21:21:21.703  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/editGoodsSubmit]}" onto public java.lang.String com.squirrel.controller.GoodsController.editGoodsSubmit(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.Goods) throws java.lang.Exception
2025-09-09 21:21:21.703  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/uploadFile]}" onto public java.util.Map<java.lang.String, java.lang.Object> com.squirrel.controller.GoodsController.uploadFile(javax.servlet.http.HttpSession,org.springframework.web.multipart.MultipartFile) throws java.lang.IllegalStateException,java.io.IOException
2025-09-09 21:21:21.703  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/publishGoodsSubmit]}" onto public java.lang.String com.squirrel.controller.GoodsController.publishGoodsSubmit(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.Image,com.squirrel.pojo.Goods,org.springframework.web.multipart.MultipartFile) throws java.lang.Exception
2025-09-09 21:21:21.703  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/api/v1/users]}" onto public com.squirrel.util.UserGrid com.squirrel.controller.MainController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String)
2025-09-09 21:21:21.704  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/goods/list],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.goodsList(javax.servlet.http.HttpServletRequest,int,int,java.lang.String,org.springframework.ui.Model)
2025-09-09 21:21:21.705  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/login],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.login(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:21:21.705  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/user/list],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.userList(javax.servlet.http.HttpServletRequest,int,org.springframework.ui.Model)
2025-09-09 21:21:21.706  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/addUser],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.addUser(com.squirrel.pojo.User)
2025-09-09 21:21:21.706  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/addUser]}" onto public java.lang.String com.squirrel.controller.UserController.addUser(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User)
2025-09-09 21:21:21.707  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/allGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.goods(javax.servlet.http.HttpServletRequest)
2025-09-09 21:21:21.707  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/logout]}" onto public java.lang.String com.squirrel.controller.UserController.logout(javax.servlet.http.HttpServletRequest)
2025-09-09 21:21:21.707  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/login]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.loginValidate(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:21:21.707  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/login],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.login(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-09-09 21:21:21.707  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/updateUser],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.updateUser(com.squirrel.pojo.User)
2025-09-09 21:21:21.707  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/deleteUser/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.deleteUser(int)
2025-09-09 21:21:21.708  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/freezeUser/{id}],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.freezeUser(int)
2025-09-09 21:21:21.708  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/unfreezeUser/{id}],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.unfreezeUser(int)
2025-09-09 21:21:21.708  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/updateInfo]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.updateInfo(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:21:21.708  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/basic]}" onto public java.lang.String com.squirrel.controller.UserController.basic(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:21:21.708  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/changeName]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.changeName(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:21:21.708  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/home]}" onto public java.lang.String com.squirrel.controller.UserController.home(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:21:21.710  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-09-09 21:21:21.710  INFO 20100 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-09-09 21:21:21.728  INFO 20100 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/upload/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:21:21.728  INFO 20100 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:21:21.728  INFO 20100 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:21:21.756  INFO 20100 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:21:22.079  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Registering beans for JMX exposure on startup
2025-09-09 21:21:22.080  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'dataSource' has been autodetected for JMX exposure
2025-09-09 21:21:22.080  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'statFilter' has been autodetected for JMX exposure
2025-09-09 21:21:22.080  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'wallFilter' has been autodetected for JMX exposure
2025-09-09 21:21:22.083  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.pool:name=dataSource,type=DruidDataSource]
2025-09-09 21:21:22.084  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'wallFilter': registering with JMX server as MBean [com.alibaba.druid.wall:name=wallFilter,type=WallFilter]
2025-09-09 21:21:22.085  INFO 20100 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
2025-09-09 21:21:22.113  INFO 20100 --- [main] s.b.c.e.t.TomcatEmbeddedServletContainer : Tomcat started on port(s): 8080 (http)
2025-09-09 21:21:22.117  INFO 20100 --- [main] com.squirrel.Application                 : Started Application in 2.159 seconds (JVM running for 3.428)
2025-09-09 21:22:17.078  INFO 20100 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-09-09 21:22:17.078  INFO 20100 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization started
2025-09-09 21:22:17.098  INFO 20100 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization completed in 20 ms
2025-09-09 21:23:21.163 ERROR 20100 --- [http-nio-8080-exec-4] com.alibaba.druid.pool.DruidDataSource   : init datasource error, url: ********************************************************************************

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 844 milliseconds ago.  The last packet sent successfully to the server was 841 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:684) [druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:991) [druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:987) [druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:103) [druid-1.0.19.jar:1.0.19]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111) [spring-jdbc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77) [spring-jdbc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:82) [mybatis-spring-1.3.1.jar:1.3.1]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:68) [mybatis-spring-1.3.1.jar:1.3.1]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:336) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:84) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) [mybatis-3.4.4.jar:3.4.4]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:143) [pagehelper-5.1.2.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) [mybatis-3.4.4.jar:3.4.4]
	at com.sun.proxy.$Proxy81.query(Unknown Source) [na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) [mybatis-3.4.4.jar:3.4.4]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_411]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_411]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) [mybatis-spring-1.3.1.jar:1.3.1]
	at com.sun.proxy.$Proxy68.selectList(Unknown Source) [na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) [mybatis-spring-1.3.1.jar:1.3.1]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:137) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:75) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) [mybatis-3.4.4.jar:3.4.4]
	at com.sun.proxy.$Proxy73.selectByCatelogOrderByDate(Unknown Source) [na:na]
	at com.squirrel.service.impl.GoodsServiceImpl.getGoodsByCatelogOrderByDate(GoodsServiceImpl.java:66) [classes/:na]
	at com.squirrel.controller.GoodsController.homeGoods(GoodsController.java:59) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_411]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_411]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:635) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123) [druid-1.0.19.jar:1.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [na:1.8.0_411]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [na:1.8.0_411]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_411]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 106 common frames omitted

2025-09-09 21:23:21.166 ERROR 20100 --- [http-nio-8080-exec-4] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} init error

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 844 milliseconds ago.  The last packet sent successfully to the server was 841 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:684) [druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:991) [druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:987) [druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:103) [druid-1.0.19.jar:1.0.19]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111) [spring-jdbc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77) [spring-jdbc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:82) [mybatis-spring-1.3.1.jar:1.3.1]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:68) [mybatis-spring-1.3.1.jar:1.3.1]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:336) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:84) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) [mybatis-3.4.4.jar:3.4.4]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:143) [pagehelper-5.1.2.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) [mybatis-3.4.4.jar:3.4.4]
	at com.sun.proxy.$Proxy81.query(Unknown Source) [na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) [mybatis-3.4.4.jar:3.4.4]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_411]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_411]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) [mybatis-spring-1.3.1.jar:1.3.1]
	at com.sun.proxy.$Proxy68.selectList(Unknown Source) [na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) [mybatis-spring-1.3.1.jar:1.3.1]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:137) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:75) [mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) [mybatis-3.4.4.jar:3.4.4]
	at com.sun.proxy.$Proxy73.selectByCatelogOrderByDate(Unknown Source) [na:na]
	at com.squirrel.service.impl.GoodsServiceImpl.getGoodsByCatelogOrderByDate(GoodsServiceImpl.java:66) [classes/:na]
	at com.squirrel.controller.GoodsController.homeGoods(GoodsController.java:59) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_411]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_411]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:635) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846) [spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123) [druid-1.0.19.jar:1.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [na:1.8.0_411]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [na:1.8.0_411]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_411]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 106 common frames omitted

2025-09-09 21:23:21.168  INFO 20100 --- [http-nio-8080-exec-4] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-09-09 21:23:21.171 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.174 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.174 ERROR 20100 --- [http-nio-8080-exec-4] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Could not get JDBC Connection; nested exception is com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 844 milliseconds ago.  The last packet sent successfully to the server was 841 milliseconds ago.
### The error may exist in file [E:\Documents\springboot-squirrel-master\springboot-squirrel-master\target\classes\mapper\GoodsMapper.xml]
### The error may involve com.squirrel.dao.GoodsMapper.selectByCatelogOrderByDate
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Could not get JDBC Connection; nested exception is com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 844 milliseconds ago.  The last packet sent successfully to the server was 841 milliseconds ago.] with root cause

javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:684) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:991) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:987) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:103) ~[druid-1.0.19.jar:1.0.19]
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111) ~[spring-jdbc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77) ~[spring-jdbc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:82) ~[mybatis-spring-1.3.1.jar:1.3.1]
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:68) ~[mybatis-spring-1.3.1.jar:1.3.1]
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:336) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:84) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.4.4.jar:3.4.4]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:143) ~[pagehelper-5.1.2.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.4.jar:3.4.4]
	at com.sun.proxy.$Proxy81.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.4.jar:3.4.4]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_411]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_411]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.1.jar:1.3.1]
	at com.sun.proxy.$Proxy68.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.1.jar:1.3.1]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:137) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:75) ~[mybatis-3.4.4.jar:3.4.4]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.4.jar:3.4.4]
	at com.sun.proxy.$Proxy73.selectByCatelogOrderByDate(Unknown Source) ~[na:na]
	at com.squirrel.service.impl.GoodsServiceImpl.getGoodsByCatelogOrderByDate(GoodsServiceImpl.java:66) ~[classes/:na]
	at com.squirrel.controller.GoodsController.homeGoods(GoodsController.java:59) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_411]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_411]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:133) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:97) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:967) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:901) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:861) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:635) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846) ~[spring-webmvc-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) ~[tomcat-embed-websocket-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123) ~[druid-1.0.19.jar:1.0.19]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:108) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:81) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) ~[spring-web-4.3.13.RELEASE.jar:4.3.13.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199) ~[tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:803) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1459) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [na:1.8.0_411]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [na:1.8.0_411]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.23.jar:8.5.23]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_411]

2025-09-09 21:23:21.177 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 2 milliseconds ago.  The last packet sent successfully to the server was 2 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.180 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.183 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.186 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 0 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.189 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 2 milliseconds ago.  The last packet sent successfully to the server was 2 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.192 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.196 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.199 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.202 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.206 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 0 milliseconds ago.  The last packet sent successfully to the server was 0 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.208 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.212 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.215 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 2 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_411]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_411]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 18 common frames omitted

2025-09-09 21:23:21.221 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.225 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.229 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 0 milliseconds ago.  The last packet sent successfully to the server was 0 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.233 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.237 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.241 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.245 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.249 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.253 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.257 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.262 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 2 milliseconds ago.  The last packet sent successfully to the server was 2 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.266 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 2 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.269 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.274 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.278 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:21.282 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 2 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:23:51.300 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:24:21.312 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:24:51.318 ERROR 20100 --- [Druid-ConnectionPool-Create-1547338771] com.alibaba.druid.pool.DruidDataSource   : create connection error, url: ********************************************************************************, errorCode 0, state 08S01

com.mysql.jdbc.exceptions.jdbc4.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1 milliseconds ago.  The last packet sent successfully to the server was 1 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor53.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.SQLError.createCommunicationsException(SQLError.java:989) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:203) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.negotiateSSLConnection(MysqlIO.java:4901) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.proceedHandshakeWithPluggableAuthentication(MysqlIO.java:1659) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.MysqlIO.doHandshake(MysqlIO.java:1226) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.coreConnect(ConnectionImpl.java:2191) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:2222) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:2017) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:779) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.JDBC4Connection.<init>(JDBC4Connection.java:47) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at sun.reflect.GeneratedConstructorAccessor47.newInstance(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_411]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_411]
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:389) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.mysql.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:330) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1408) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1464) ~[druid-1.0.19.jar:1.0.19]
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:1995) ~[druid-1.0.19.jar:1.0.19]
Caused by: javax.net.ssl.SSLHandshakeException: No appropriate protocol (protocol is disabled or cipher suites are inappropriate)
	at sun.security.ssl.HandshakeContext.<init>(HandshakeContext.java:171) ~[na:1.8.0_411]
	at sun.security.ssl.ClientHandshakeContext.<init>(ClientHandshakeContext.java:106) ~[na:1.8.0_411]
	at sun.security.ssl.TransportContext.kickstart(TransportContext.java:245) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:410) ~[na:1.8.0_411]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:389) ~[na:1.8.0_411]
	at com.mysql.jdbc.ExportControlled.transformSocketToSSLSocket(ExportControlled.java:188) ~[mysql-connector-java-5.1.44.jar:5.1.44]
	... 17 common frames omitted

2025-09-09 21:24:58.402  WARN 20100 --- [http-nio-8080-exec-6] com.alibaba.druid.pool.DruidDataSource   : not full timeout retry : 1
2025-09-09 21:25:14.719  INFO 20100 --- [Thread-7] ationConfigEmbeddedWebApplicationContext : Closing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@3bbc39f8: startup date [Tue Sep 09 21:21:20 CST 2025]; root of context hierarchy
2025-09-09 21:25:14.720  INFO 20100 --- [Thread-7] o.s.j.e.a.AnnotationMBeanExporter        : Unregistering JMX-exposed beans on shutdown
2025-09-09 21:25:14.721  INFO 20100 --- [Thread-7] o.s.j.e.a.AnnotationMBeanExporter        : Unregistering JMX-exposed beans
2025-09-09 21:25:14.722  INFO 20100 --- [Thread-7] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-09-09 21:25:19.509  INFO 33696 --- [main] com.squirrel.Application                 : Starting Application on LC-远海 with PID 33696 (E:\Documents\springboot-squirrel-master\springboot-squirrel-master\target\classes started by Bart in E:\Documents\springboot-squirrel-master\springboot-squirrel-master)
2025-09-09 21:25:19.511  INFO 33696 --- [main] com.squirrel.Application                 : No active profile set, falling back to default profiles: default
2025-09-09 21:25:19.622  INFO 33696 --- [main] ationConfigEmbeddedWebApplicationContext : Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@3bbc39f8: startup date [Tue Sep 09 21:25:19 CST 2025]; root of context hierarchy
2025-09-09 21:25:19.867  INFO 33696 --- [main] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'dataSource' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=druidConfig; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/squirrel/config/DruidConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=application; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=close; defined in com.squirrel.Application]
2025-09-09 21:25:20.336  INFO 33696 --- [main] s.b.c.e.t.TomcatEmbeddedServletContainer : Tomcat initialized with port(s): 8080 (http)
2025-09-09 21:25:20.341  INFO 33696 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-09 21:25:20.342  INFO 33696 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet Engine: Apache Tomcat/8.5.23
2025-09-09 21:25:20.461  INFO 33696 --- [localhost-startStop-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-09 21:25:20.462  INFO 33696 --- [localhost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 840 ms
2025-09-09 21:25:20.603  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'statViewServlet' to [/druid/*]
2025-09-09 21:25:20.605  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.ServletRegistrationBean  : Mapping servlet: 'dispatcherServlet' to [/]
2025-09-09 21:25:20.606  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-09 21:25:20.607  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-09 21:25:20.607  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-09-09 21:25:20.607  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'requestContextFilter' to: [/*]
2025-09-09 21:25:20.607  INFO 33696 --- [localhost-startStop-1] o.s.b.w.servlet.FilterRegistrationBean   : Mapping filter: 'webStatFilter' to urls: [/*]
2025-09-09 21:25:21.022  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@3bbc39f8: startup date [Tue Sep 09 21:25:19 CST 2025]; root of context hierarchy
2025-09-09 21:25:21.054  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/deleteComments/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.deleteComments(int)
2025-09-09 21:25:21.055  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/addComments],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.addaddComments(com.squirrel.pojo.Comments)
2025-09-09 21:25:21.055  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comments/api/updateComments],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.CommentsController.updateComments(com.squirrel.pojo.Comments)
2025-09-09 21:25:21.057  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/publishGoodsSubmit]}" onto public java.lang.String com.squirrel.controller.GoodsController.publishGoodsSubmit(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.Image,com.squirrel.pojo.Goods,org.springframework.web.multipart.MultipartFile) throws java.lang.Exception
2025-09-09 21:25:21.057  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/catelog/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.catelogGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.String) throws java.lang.Exception
2025-09-09 21:25:21.057  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/editGoods/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.editGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer) throws java.lang.Exception
2025-09-09 21:25:21.057  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/editGoodsSubmit]}" onto public java.lang.String com.squirrel.controller.GoodsController.editGoodsSubmit(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.Goods) throws java.lang.Exception
2025-09-09 21:25:21.058  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/homeGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.homeGoods(javax.servlet.http.HttpServletRequest) throws java.lang.Exception
2025-09-09 21:25:21.058  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/search]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.searchGoods(javax.servlet.http.HttpServletRequest,java.lang.String) throws java.lang.Exception
2025-09-09 21:25:21.058  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/goodsId/{id}]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.getGoodsById(javax.servlet.http.HttpServletRequest,java.lang.Integer,java.lang.String) throws java.lang.Exception
2025-09-09 21:25:21.058  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/deleteGoods/{id}]}" onto public java.lang.String com.squirrel.controller.GoodsController.deleteGoods(javax.servlet.http.HttpServletRequest,java.lang.Integer) throws java.lang.Exception
2025-09-09 21:25:21.058  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/offGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.GoodsController.offGoods() throws java.lang.Exception
2025-09-09 21:25:21.058  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/api/offGoods/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.GoodsController.offGoods(int)
2025-09-09 21:25:21.059  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/publishGoods]}" onto public java.lang.String com.squirrel.controller.GoodsController.publishGoods(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:25:21.059  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/uploadFile]}" onto public java.util.Map<java.lang.String, java.lang.Object> com.squirrel.controller.GoodsController.uploadFile(javax.servlet.http.HttpSession,org.springframework.web.multipart.MultipartFile) throws java.lang.IllegalStateException,java.io.IOException
2025-09-09 21:25:21.059  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/api/updateGoods],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.GoodsController.updateGoods(com.squirrel.pojo.Goods)
2025-09-09 21:25:21.059  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/api/v1/users]}" onto public com.squirrel.util.UserGrid com.squirrel.controller.MainController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String)
2025-09-09 21:25:21.061  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/user/list],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.userList(javax.servlet.http.HttpServletRequest,int,org.springframework.ui.Model)
2025-09-09 21:25:21.062  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/goods/list],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.goodsList(javax.servlet.http.HttpServletRequest,int,int,java.lang.String,org.springframework.ui.Model)
2025-09-09 21:25:21.062  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manage/login],methods=[GET]}" onto public java.lang.String com.squirrel.controller.ManageController.login(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:25:21.063  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/changeName]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.changeName(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/basic]}" onto public java.lang.String com.squirrel.controller.UserController.basic(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/home]}" onto public java.lang.String com.squirrel.controller.UserController.home(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/addUser]}" onto public java.lang.String com.squirrel.controller.UserController.addUser(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/addUser],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.addUser(com.squirrel.pojo.User)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/allGoods]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.goods(javax.servlet.http.HttpServletRequest)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/login],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.login(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-09-09 21:25:21.064  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/login]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.loginValidate(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:25:21.065  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/updateUser],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.updateUser(com.squirrel.pojo.User)
2025-09-09 21:25:21.065  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/freezeUser/{id}],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.freezeUser(int)
2025-09-09 21:25:21.065  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/logout]}" onto public java.lang.String com.squirrel.controller.UserController.logout(javax.servlet.http.HttpServletRequest)
2025-09-09 21:25:21.065  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/deleteUser/{id}],methods=[DELETE]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.deleteUser(int)
2025-09-09 21:25:21.065  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/api/unfreezeUser/{id}],methods=[POST]}" onto public com.squirrel.dto.AjaxResult com.squirrel.controller.UserController.unfreezeUser(int)
2025-09-09 21:25:21.066  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/user/updateInfo]}" onto public org.springframework.web.servlet.ModelAndView com.squirrel.controller.UserController.updateInfo(javax.servlet.http.HttpServletRequest,com.squirrel.pojo.User,org.springframework.ui.ModelMap)
2025-09-09 21:25:21.066  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-09-09 21:25:21.067  INFO 33696 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-09-09 21:25:21.083  INFO 33696 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/upload/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:25:21.084  INFO 33696 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:25:21.084  INFO 33696 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:25:21.110  INFO 33696 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-09-09 21:25:21.435  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Registering beans for JMX exposure on startup
2025-09-09 21:25:21.436  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'dataSource' has been autodetected for JMX exposure
2025-09-09 21:25:21.436  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'statFilter' has been autodetected for JMX exposure
2025-09-09 21:25:21.436  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with name 'wallFilter' has been autodetected for JMX exposure
2025-09-09 21:25:21.438  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.pool:name=dataSource,type=DruidDataSource]
2025-09-09 21:25:21.440  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'wallFilter': registering with JMX server as MBean [com.alibaba.druid.wall:name=wallFilter,type=WallFilter]
2025-09-09 21:25:21.441  INFO 33696 --- [main] o.s.j.e.a.AnnotationMBeanExporter        : Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
2025-09-09 21:25:21.468  INFO 33696 --- [main] s.b.c.e.t.TomcatEmbeddedServletContainer : Tomcat started on port(s): 8080 (http)
2025-09-09 21:25:21.471  INFO 33696 --- [main] com.squirrel.Application                 : Started Application in 2.109 seconds (JVM running for 3.333)
