<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8" />
    <title>广财大二手工坊</title>
    <link rel="stylesheet" th:href="@{/css/index.css}" />

    <!--<script type="text/javascript" th:src="@{/js/jquery.js}" ></script>-->
    script type="text/javascript" th:src="@{/js/jquery-3.2.1.min.js}"></script>

    <script type="text/javascript" th:src="@{/js/materialize.min.js}" ></script>
    <script type="text/javascript" th:src="@{/js/index.bundle.js}" ></script>
    <link rel="stylesheet" th:href="@{/css/materialize-icon.css}" />
</head>
<body ng-view="ng-view">
<script>
    function showLogin() {
        if($("#signup-show").css("display")=='block'){
            $("#signup-show").css("display","none");
        }
        if($("#login-show").css("display")=='none'){
            $("#login-show").css("display","block");
        }else{
            $("#login-show").css("display","none");
        }
    }
    function showSignup() {
        if($("#login-show").css("display")=='block'){
            $("#login-show").css("display","none");
        }
        if($("#signup-show").css("display")=='none'){
            $("#signup-show").css("display","block");
        }else{
            $("#signup-show").css("display","none");
        }
    }
    function ChangeName() {
        if($("#changeName").css("display")=='none'){
            $("#changeName").css("display","block");
        }else{
            $("#changeName").css("display","none");
        }
    }
    <!-- TODO::IDEA关于thymeleaf的支持红色波浪线 -->
</script>
<!--
    时间：2018/01/05 22:02:15
    描述：顶部
-->
<div ng-controller="headerController" class="header stark-components navbar-fixed ng-scope">
    <nav class="white nav1">
        <div class="nav-wrapper">
            <a th:href="@{/goods/homeGoods}" class="logo">
                <!--<em class="em1">广财大</em>-->
                <em class="em1">
                    <span style="color: #4b82c3;">广</span>
                    <span style="color: #ffd51f;">财</span>
                    <span style="color: #b02b1a;">大</span>
                </em>
                <em class="em2">二手工坊</em>
                <em class="em3">
                    <span style="color: #d80000;">g</span>
                    <span style="color: #48bdff;">d</span>
                    <span style="color: #ea83ee;">u</span>
                    <span style="color: #50dd5a;">f</span>
                    <span style="color: #ee9946;">e</span>
                    .market
                </em>
                <!--<em class="em3">gdufe.market</em>-->
            </a>
            <div class="nav-wrapper search-bar">
                <form ng-submit="search()" class="ng-pristine ng-invalid ng-invalid-required" th:action="@{/goods/search}">
                    <div class="input-field">
                        <input id="search" name="str" th:value="${search}" placeholder="搜点什么吧" style="height: 40px;"
                               class="ng-pristine ng-untouched ng-empty ng-invalid ng-invalid-required"/>
                        <label for="search" class="active">
                            <i ng-click="search()" class="iconfont"></i>
                        </label>
                    </div>
                </form>
            </div>
            <ul class="right" th:if="${cur_user != null}">
                <li class="publish-btn">
                    <button data-position="bottom" class="red lighten-1 waves-effect waves-light btn">
                        <a th:href="@{/goods/publishGoods}">我要发布</a>
                    </button>
                </li>
                <li>
                    <a th:href="@{/user/allGoods}">我发布的商品</a>
                </li>
                <li>
                    <a th:text="${cur_user.username}"></a>
                </li>
                <li class="notification">
                    <i ng-click="showNotificationBox()" class="iconfont"></i>
                    <div ng-show="notification.tagIsShow" class="notification-amount red lighten-1 ng-binding ng-hide">0 </div>
                </li>
                <li class="changemore">
                    <a class="changeMoreVertShow()">
                        <i class="iconfont"></i>
                    </a>
                    <div class="more-vert">
                        <ul class="dropdown-content">
                            <li><a th:href="@{/user/home}">个人中心</a></li>
                            <li><a>消息</a></li>
                            <li><a th:onclick="'ChangeName()'">更改用户名</a></li>
                            <li><a th:href="@{/user/logout}">退出登录</a></li>
                        </ul>
                    </div>
                </li>
            </ul>
            <ul class="right" th:if="${cur_user == null}">
                <li class="publish-btn">
                    <button th:onclick="'showLogin()'" data-position="bottom" data-delay="50"
                            data-tooltip="需要先登录哦！" class="red lighten-1 waves-effect waves-light btn" data-tooltip-id="510d3084-e666-f82f-3655-5eae4304a83a" >
                        我要发布</button>
                </li>
                <li>
                    <a th:onclick="'showLogin()'">登录</a>
                </li>
                <li>
                    <a th:onclick="'showSignup()'">注册</a>
                </li>
            </ul>
        </div>
    </nav>
</div>
<!--
    时间：2018/01/05 23:22:19
    描述：登录
-->
<div ng-controller="loginController" class="ng-scope">
    <div id="login-show" class="login stark-components">
        <div class="publish-box z-depth-4">
            <div class="row">
                <a th:onclick="'showLogin()'">
                    <div class="col s12 title"></div>
                </a>
                <form th:action="@{/user/login}" method="post" commandName="user" role="form">
                    <div class="input-field col s12">
                        <input type="text" name="phone" required="required" pattern="^1[0-9]{10}$" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>手机</label>
                    </div>
                    <div class="input-field col s12">
                        <input type="password" name="password" required="required" class="validate ng-pristine ng-untouched ng-empty ng-invalid ng-invalid-required" />
                        <label>密码</label>
                        <a ng-click="showForget()" class="forget-btn">忘记密码？</a>
                    </div>
                    <button type="submit" class="waves-effect waves-light btn login-btn red lighten-1">
                        <i class="iconfont left"></i>
                        <em>登录</em>
                    </button>
                    <div class="col s12 signup-area">
                        <em>没有账号？赶快</em>
                        <a th:onclick="'showSignup()'" class="signup-btn">注册</a>
                        <em>吧！</em>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--
    时间：2018/01/05 23:26:05
    描述：注册
-->
<div ng-controller="signupController" class="ng-scope">
    <div id="signup-show" class="signup stark-components">
        <div class="publish-box z-depth-4">
            <div class="row">
                <a th:onclick="'showSignup()'">
                    <div class="col s12 title"></div>
                </a>
                <form th:action="@{/user/addUser}" method="post" commandName="user" role="form">
                    <div class="input-field col s12">
                        <input type="text" name="username" required="required" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>昵称</label>
                    </div>
                    <div class="input-field col s12">
                        <input type="text" name="phone" required="required" pattern="^1[0-9]{10}$" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>手机</label>
                    </div>
                    <div class="input-field col s12">
                        <input type="password" name="password" required="required" class="validate ng-pristine ng-untouched ng-empty ng-invalid ng-invalid-required" />
                        <label>密码</label>
                    </div>
                    <div ng-show="checkTelIsShow" class="col s12">
                        <button type="submit" class="waves-effect waves-light btn verify-btn red lighten-1">
                            <i class="iconfont left"></i>
                            <em>点击注册</em>
                        </button>
                    </div>
                    <div ng-show="checkTelIsShow" class="login-area col s12">
                        <em>已有账号？去</em>
                        <a th:onclick="'showLogin()'">登录</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--
    时间：2018/01/05 23:36:04
    描述：更改用户名
-->
<div ng-controller="changeNameController" class="ng-scope">
    <div id="changeName" class="change-name stark-components">
        <div class="publish-box z-depth-4">
            <div class="row">
                <div class="col s12 title">
                    <h1>修改用户名</h1>
                </div>
                <form th:action="@{/user/changeName}" method="post" commandName="user" role="form">
                    <div class="input-field col s12">
                        <input type="text" name="username" required="required" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>修改用户名</label>
                    </div>
                    <div ng-show="checkTelIsShow" class="col s12">
                        <button class="waves-effect waves-light btn publish-btn red lighten-1">
                            <i class="iconfont left"></i>
                            <em>确认</em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--
    时间：2018/01/05 23:36:58
    描述：左侧导航条
-->
<div ng-controller="sidebarController" class="sidebar stark-components ng-scope">
    <li ng-class="{true: 'active'}[isAll]">
        <a th:href="@{/goods/catelog/1}" class="index">
            <img th:src="@{/img/index.png}" />
            <em>最新发布</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isDigital]">
        <a th:href="@{/goods/catelog/1}" class="digital">
            <img th:src="@{/img/digital.png}"  />
            <em>闲置数码</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isRide]">
        <a th:href="@{/goods/catelog/2}" class="ride">
            <img th:src="@{/img/ride.png}"/>
            <em>校园代步</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isCommodity]">
        <a th:href="@{/goods/catelog/3}" class="commodity">
            <img th:src="@{/img/commodity.png}"/>
            <em>电器日用</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isBook]">
        <a th:href="@{/goods/catelog/4}" class="book">
            <img th:src="@{/img/book.png}"/>
            <em>图书教材</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isMakeup]">
        <a th:href="@{/goods/catelog/5}" class="makeup">
            <img th:src="@{/img/makeup.png}"/>
            <em>美妆衣物</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isSport]">
        <a th:href="@{/goods/catelog/6}" class="sport">
            <img th:src="@{/img/sport.png}"/>
            <em>运动棋牌</em>
        </a>
    </li>
    <li ng-class="{true: 'active'}[isSmallthing]">
        <a th:href="@{/goods/catelog/7}" class="smallthing">
            <img th:src="@{/img/smallthing.png}"/>
            <em>票券小物</em>
        </a>
    </li>
    <div class="info">
        <a href="" target="_blank">关于我们</a><em>-</em>
        <a href="">联系我们</a>
        <p>©2018 GDUFE</p>
    </div>
</div>
<!--
    时间：2018/01/06 10:55:09
    描述：右侧显示部分
-->
<div class="main-content">
    <!--
        时间：2018/01/06 11:05:20
        描述：搜索结果
    -->
    <div class="index-title">
        <a href="">搜索结果</a>
        <hr class="hr1" />
        <hr class="hr2" />
    </div>
    <div class="waterfoo stark-components row">
        <div class="item-wrapper normal" th:each="item : ${goodsExtendList}">
            <div class="card col">
                <a th:href="@{'/goods/goodsId/'+${item.goods.id}}">
                    <div class="card-image">
                        <img th:src="@{'/upload/'+${item.images[0].imgUrl}}" />
                    </div>
                    <div class="card-content item-price" th:text="${item.goods.price}"></div>
                    <div class="card-content item-name">
                        <p th:text="${item.goods.name}"></p>
                    </div>
                    <div class="card-content item-location">
                        <p>广东财经大学</p>
                        <p th:text="${item.goods.startTime}"></p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
</body>
</html>