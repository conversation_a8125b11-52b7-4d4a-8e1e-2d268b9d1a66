<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.squirrel.dao.ImageMapper" >
    <resultMap id="BaseResultMap" type="com.squirrel.pojo.Image" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="goods_id" property="goodsId" jdbcType="INTEGER" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.squirrel.pojo.Image" extends="BaseResultMap" >
        <result column="img_url" property="imgUrl" jdbcType="LONGVARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, goods_id
    </sql>
    <sql id="Blob_Column_List" >
        img_url
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from t_shop_image
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByGoodsPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer">
        select * from t_shop_image where goods_id = #{goodsId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from t_shop_image
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteImagesByGoodsPrimaryKey" parameterType="java.lang.Integer">
        delete from t_shop_image
        where goods_id = #{goodsId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.squirrel.pojo.Image" >
        insert into t_shop_image (id, goods_id, img_url
        )
        values (#{id,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, #{imgUrl,jdbcType=LONGVARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.squirrel.pojo.Image" >
        insert into t_shop_image
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="goodsId != null" >
                goods_id,
            </if>
            <if test="imgUrl != null" >
                img_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null" >
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="imgUrl != null" >
                #{imgUrl,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.squirrel.pojo.Image" >
        update t_shop_image
        <set >
            <if test="goodsId != null" >
                goods_id = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="imgUrl != null" >
                img_url = #{imgUrl,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.squirrel.pojo.Image" >
        update t_shop_image
        set goods_id = #{goodsId,jdbcType=INTEGER},
        img_url = #{imgUrl,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.squirrel.pojo.Image" >
        update t_shop_image
        set goods_id = #{goodsId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>