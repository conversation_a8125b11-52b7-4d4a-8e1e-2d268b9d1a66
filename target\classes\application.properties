#server
server.port=8080

# ��־�ļ�·��
logging.path=./logs
spring.thymeleaf.prefix=classpath:/templates

#ָ��bean���ڰ�
mybatis.type-aliases-package=com.squirrel.pojo
#ָ��ӳ���ļ�
mybatis.mapperLocations=classpath:mapper/*.xml

#��ҳ���,pagehelper
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql

# MySQL
#spring.datasource.url = *****************************************************************************************
#spring.datasource.username = root
#spring.datasource.password = 123456
#spring.datasource.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.url = *********************************************************************************************************************************************

spring.datasource.username = root
spring.datasource.password = zhou6666
spring.datasource.driver-class-name = com.mysql.jdbc.Driver


#���ݿ����ӳ�Druid
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# ��ʼ����С����С�����
spring.datasource.initialSize=5
spring.datasource.minIdle=5
spring.datasource.maxActive=20
# ���û�ȡ���ӵȴ���ʱ��ʱ��
spring.datasource.maxWait=60000
# ���ü����òŽ���һ�μ�⣬�����Ҫ�رյĿ������ӣ���λ�Ǻ���
spring.datasource.timeBetweenEvictionRunsMillis=60000
# ����һ�������ڳ�����С�����ʱ�䣬��λ�Ǻ���
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
# ��PSCache������ָ��ÿ��������PSCache�Ĵ�С
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
# ���ü��ͳ�����ص�filters��ȥ�����ؽ���sql�޷�ͳ�ƣ�'wall'���ڷ���ǽ
spring.datasource.filters=stat,wall,log4j
# �ϲ����DruidDataSource�ļ������
#spring.datasource.useGlobalDataSourceStat=true

# �ļ���С
spring.http.multipart.maxFileSize = 10Mb
spring.http.multipart.maxRequestSize=100Mb
