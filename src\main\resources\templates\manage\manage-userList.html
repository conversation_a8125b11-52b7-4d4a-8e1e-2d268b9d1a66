<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>广财大二手工坊控制台-用户管理</title>
    <!--<link rel="stylesheet" th:href="@{https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css}" />-->
    <link rel="stylesheet" th:href="@{/css/bootstrap/bootstrap.min.css}" />

    <!--<link th:href="@{https://cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css}" rel="stylesheet" />-->
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet" />

    <link rel="stylesheet" th:href="@{/css/manage/manage-app.css}" />

    <!--<script type="text/javascript" th:src="@{https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js}"></script>-->
    <script type="text/javascript" th:src="@{/js/jquery-3.2.1.min.js}"></script>

    <!--<script type="text/javascript" th:src="@{https://cdn.bootcss.com/bootstrap/3.3.7/js/bootstrap.min.js}"></script>-->
    <script type="text/javascript" th:src="@{/js/bootstrap/bootstrap.min.js}"></script>

    <script type="text/javascript" th:src="@{/js/manage/manage-app.js}"></script>
    <script type="text/javascript" th:src="@{/js/manage/manage-userList.js}"></script>
</head>

<body>

    <nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container-fluid">
        <div class="navbar-header">
        <a class="navbar-brand" href="#"><img style="width: 32px;height: 32px;" th:src="@{/img/manage-logo.png}" /></a>
        <a class="navbar-brand" href="#">广财大二手工坊控制台</a>
    </div>
    <div id="navbar" class="navbar-collapse collapse">
        <ul class="nav navbar-nav navbar-right">
        <li class="dropdown">
        <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
        <img class="img-circle" style="width: 32px;height: 32px;" th:src="@{/img/photo.jpg}" />&nbsp;&nbsp;<span th:text="${cur_user.username}"></span>
        <span class="caret"></span>
        </a>
        <ul class="dropdown-menu">
        <li><a href="javascript:void(0);">修改密码</a></li>
        <li><a th:href="@{/user/logout}">退出系统</a></li>
        </ul>
        </li>
        </ul>
    </div>
    </div>
    </nav>
    <div class="container-fluid">
    <div class="row">
    <div class="col-sm-3 col-md-2 sidebar">
    <ul class="nav nav-sidebar">
    <li class="active"><a href="#"><span class="glyphicon glyphicon-user"></span>&nbsp;用户管理 <span class="sr-only">(current)</span></a></li>
    <li><a th:href="@{/manage/goods/list}"><i class="fa fa-shopping-bag"></i>&nbsp;商品管理</a></li>
    <li><a href="#"><i class="fa fa-cubes"></i>&nbsp;商品审核</a></li>
    </ul>
    </div>
    <div class="col-sm-9 col-sm-offset-3 col-md-10 col-md-offset-2 main">
    <h1 class="page-header">用户管理</h1>
    <div class="row" style="margin-bottom: 5px;">
    <div class="col-md-10">
    </div>
    <div class="col-md-2">
    <button type="button" class="btn btn-success" id="addUserBtn"><i class="fa fa-plus"></i>添加用户</button>
    </div>
    </div>
    <div class="table-responsive">
    <table class="table table-striped table-bordered">
    <thead>
    <tr>
    <th>头像</th>
    <th>用户名</th>
    <th>手机号</th>
    <th>QQ</th>
    <th>注册时间</th>
    <th>在线商品数量</th>
    <th>权限</th>
    <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <tr th:each="item,itemStats : ${data['users']}">
    <td><img th:src="@{'https://avatar.tobi.sh/tobiaslins.svg?text='+${#strings.substring(item.username,0,1)}+'&amp;size=32'}" class="img-circle" /></td>
    <td th:text="${item.username}"></td>
    <td th:text="${item.phone}"></td>
    <td th:text="${item.qq}"></td>
    <td th:text="${item.createAt}"></td>
    <td th:text="${item.goodsNum}"></td>
    <td th:if="${item.power == 10 and item.status == 0}"><span class="label label-info">普通</span></td>
    <td th:if="${item.power == 10 and item.status == 1}"><span class="label label-default">普通</span></td>
    <td th:if="${item.power == 90}"><span class="label label-warning">管理员</span></td>
    <td>
        <button type="button" th:if="${item.status == 0 and item.power == 90}" class="btn btn-default disabled btn-xs"><i class="fa fa-eye-slash"></i>冻结</button>
        <button type="button" th:if="${item.status == 0 and item.power == 10}" th:onclick="'manageUserListPage.freezeUserAction('+${item.id}+')'" class="btn btn-danger btn-xs"><i class="fa fa-eye-slash"></i>冻结</button>
        <button type="button" th:if="${item.status == 1}" th:onclick="'manageUserListPage.unfreezeUserAction('+${item.id}+')'" class="btn btn-info btn-xs"><i class="fa fa-eye"></i>解冻</button>
        <button type="button" class="btn btn-primary btn-xs" th:onclick="'manageUserListPage.updateUserModalAction('+${itemStats.index}+')'"><i class="fa fa-edit"></i>编辑</button>
        <button type="button" class="btn btn-danger btn-xs" th:onclick="'manageUserListPage.deleteUserAction('+${item.id}+')'"><i class="fa fa-trash"></i>删除</button>
    </td>
    </tr>
    </tbody>
    </table>
    </div>
    <div class="row">
    <div class="col-md-9" id="subPageHead">
    <!--
    <ul class="pagination">
    <li class="disabled"><a href="#"><span>首页</span></a></li>
    <li class="disabled"><a href="#"><span>上一页</span></a></li>
    <li class="active"><a href="#">1</a></li>
    <li><a href="#">1</a></li>
    <li><a href="#">2</a></li>
    <li><a href="#">3</a></li>
    <li><a href="#">4</a></li>
    <li><a href="#">5</a></li>
    <li><a href="#">6</a></li>
    <li><a href="#">7</a></li>
    <li><a href="#">8</a></li>
    <li>
    <a href="#">
    <span>下一页</span>
    </a>
    </li>
    <li>
    <a href="#">
    <span>末页</span>
    </a>
    </li>
    </ul>
    -->
    </div>
    <div class="col-md-3">
    </div>
    </div>
    </div>
    </div>
    </div>
    <!--新增窗口-->
    <div id="addUserModal" class="bootbox modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog ">
    <div class="modal-content">
    <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="myModalLabel">新增用户</h4>
    </div>
    <div class="modal-body">
    <form class="form-horizontal" role="form" onsubmit="return false;">

    <div class="form-group">
        <label class="col-sm-2 control-label">昵称</label>
        <div class="col-sm-8">
            <input id="addUsername" type="text" class="form-control" placeholder="" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">手机</label>
        <div class="col-sm-8">
            <input id="addPhone" type="text" class="form-control" placeholder="" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">QQ</label>
        <div class="col-sm-8">
            <input id="addQq" type="text" class="form-control" placeholder="" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">密码</label>
        <div class="col-sm-8">
            <input id="addPassword" type="password" class="form-control" placeholder="" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">权限</label>
        <div class="col-sm-8">
            <select class="form-control" id="addPower">
                <option value="10">普通</option>
                <option value="90">管理员</option>
            </select>
           </div>
    </div>
    </form>
    </div>
    <div class="modal-footer">
    <button data-bb-handler="confirm" type="button" id="confirmAddUserBtn" class="btn btn-success radius">
    <span><i class="icon-ok"></i></span> 确定
    </button>
    <button data-bb-handler="cancel" type="button" id="cancelAddUserBtn" class="btn btn-danger radius">取消</button>
    </div>
    </div>
    </div>
    </div>

    <!-- 编辑窗口 -->
    <div id="updateUserModal" class="bootbox modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog ">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">编辑用户</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form" onsubmit="return false;">
                        <input type="hidden" id="updateUserIndex" />
                        <div class="form-group">
                                <label class="col-sm-2 control-label">昵称</label>
                                <div class="col-sm-8">
                                    <input id="updateUsername" type="text" class="form-control" placeholder="" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">手机</label>
                                <div class="col-sm-8">
                                    <input id="updatePhone" type="text" class="form-control" placeholder="" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">QQ</label>
                                <div class="col-sm-8">
                                    <input id="updateQq" type="text" class="form-control" placeholder="" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">密码</label>
                                <div class="col-sm-8">
                                    <input id="updatePassword" type="text" class="form-control" placeholder="" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">权限</label>
                                <div class="col-sm-8">
                                    <select class="form-control" id="updatePower">
                                        <option value="10">普通</option>
                                        <option value="90">管理员</option>
                                    </select>
                                   </div>
                            </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button data-bb-handler="confirm" type="button" id="confirmUpdateUserBtn" class="btn btn-success radius">
                        <span><i class="icon-ok"></i></span> 保存
                    </button>
                    <button data-bb-handler="cancel" type="button" id="cancelUpdateUserBtn" class="btn btn-danger radius">取消</button>
                </div>
            </div>
        </div>
    </div>
<script th:inline="javascript">
    /*<![CDATA[*/
    var pageNum = /*[[${data['pageNum']}]]*/;
    var pageSize = /*[[${data['pageSize']}]]*/;
    var totalPageNum = /*[[${data['totalPageNum']}]]*/;
    var totalPageSize = /*[[${data['totalPageSize']}]]*/;
    var users = /*[[${data['users']}]]*/;

    $(function(){
        app.init();
        manageUserListPage.init(pageNum, pageSize, totalPageNum, totalPageSize, users);
    });
    /*]]>*/
</script>
</body>
</html>

