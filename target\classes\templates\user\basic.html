<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" />
    <title>个人设置</title>
    <link rel="stylesheet" th:href="@{/css/font-awesome.min.css}" />
    <link rel="stylesheet" th:href="@{/css/userhome.css}" />
    <link rel="stylesheet" th:href="@{/css/user.css}" />
</head>
<body>
<div class="pre-2" id="big_img">
    <img th:src="@{http://findfun.oss-cn-shanghai.aliyuncs.com/images/head_loading.gif}" class="jcrop-preview jcrop_preview_s" />
</div>
<div id="cover" style="min-height: 639px;">
    <div id="user_area">
        <div id="home_header">
            <a th:href="@{/goods/homeGoods}">
                <h1 class="logo"></h1>
            </a>
            <a th:href="@{/user/home}">
                <div class="home"></div>
            </a>
        </div>
        <!--
            时间：2018/01/06 11:23:15
            描述：左侧个人中心栏
        -->
        <div id="user_nav">
            <div class="user_info">
                <div class="head_img">
                    <img th:src="@{/img/photo.jpg}" />
                </div>
                <div class="big_headimg">
                    <img th:src="@{/img/findfun.png}" />
                </div>
                <span class="name" th:text="${cur_user.username}"></span>
                <span class="school">广东财经大学</span>
                <span class="name" th:text="'闲置数量：'+${cur_user.goodsNum}"></span>
            </div>
            <div class="home_nav">
                <ul>
                    <a href="">
                        <li class="notice">
                            <div></div>
                            <span>我的消息</span>
                            <strong></strong>
                        </li>
                    </a>
                    <a href="">
                        <li class="fri">
                            <div></div>
                            <span>关注列表</span>
                            <strong></strong>
                        </li>
                    </a>
                    <a th:href="@{/user/basic}">
                        <li class="set">
                            <div></div>
                            <span>个人设置</span>
                            <strong></strong>
                        </li>
                    </a>
                    <a th:href="@{/goods/publishGoods}">
                        <li class="store">
                            <div></div>
                            <span>发布物品</span>
                            <strong></strong>
                        </li>
                    </a>
                    <a th:href="@{/user/allGoods}">
                        <li class="second">
                            <div></div>
                            <span>我的闲置</span>
                            <strong></strong>
                        </li>
                    </a>
                </ul>
            </div>
        </div>
        <!--
            时间：2018/01/06 11:28:20
            描述：右侧内容区域
        -->
        <div id="user_content">
            <div class="basic">
                <form th:action="@{/user/updateInfo}" method="post" commandName="user" role="form">
                    <h1>完善与修改个人信息</h1><hr />
                    <div class="changeinfo">
                        <span>昵称：</span>
                        <input class="in_info" type="text" name="username" placeholder="请输入昵称"
                               th:value="${cur_user.username}"/>
                    </div><hr />
                    <div class="changeinfo">
                        <span>开通时间：</span>
                        <input class="in_info" type="text" name="createAt"
                               th:value="${cur_user.createAt}" readonly="true"/>
                    </div><hr />
                    <div class="changeinfo">
                        <span>手机号码：</span>
                        <input class="in_info" type="text" name="phone"
                               th:value="${cur_user.phone}" readonly="true"/>
                        <span id="checkphone">已验证</span>
                    </div><hr />
                    <div class="changeinfo">
                        <span>QQ：</span>
                        <input class="in_info" type="text" name="qq" placeholder="请输入QQ"
                               th:value="${cur_user.qq}"/>
                    </div>
                    <input type="submit" class="setting-save" value="保存修改信息" />
                </form>
            </div>
            <!--
                TODO::目前是静态的
                时间：2018/01/06
                描述：最右侧，可能认识的人
            -->
            <div class="recommend">
                <div class="title">
                    <span class="text">可能认识的人</span>
                    <span class="change">换一组</span>
                    <span class="underline"></span>
                </div>
                <ul>
                    <li>
                        <a href="" class="head_img">
                            <img th:src="@{/img/photo1.jpg}" />
                        </a>
                        <span>Brudce</span>
                        <div class="fa fa-plus-square"></div>
                    </li>
                    <li>
                        <a href="" class="head_img">
                            <img th:src="@{/img/photo2.jpg}" />
                        </a>
                        <span>Graham</span>
                        <div class="fa fa-plus-square"></div>
                    </li>
                    <li>
                        <a href="" class="head_img">
                            <img th:src="@{/img/photo3.jpg}" />
                        </a>
                        <span>策马奔腾hly</span>
                        <div class="fa fa-plus-square"></div>
                    </li>
                    <li>
                        <a href="" class="head_img">
                            <img th:src="@{/img/photo4.jpg}" />
                        </a>
                        <span>Danger-XFH</span>
                        <div class="fa fa-plus-square"></div>
                    </li>
                    <li>
                        <a href="" class="head_img">
                            <img th:src="@{/img/photo5.jpg}" />
                        </a>
                        <span>Keithw</span>
                        <div class="fa fa-plus-square"></div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
</body>
</html>