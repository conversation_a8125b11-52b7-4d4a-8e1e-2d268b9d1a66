<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8" />
    <title>南工跳瘙市场</title>
    <!--<link rel="stylesheet" th:href="@{https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css}" />-->
    <link rel="stylesheet" th:href="@{/css/bootstrap/bootstrap.min.css}" />
    <link rel="stylesheet" th:href="@{/css/index.css}" />
    <!--
    <script type="text/javascript" th:src="@{/js/jquery.js}" ></script>
    -->
    <!--<script type="text/javascript" th:src="@{https://cdn.bootcss.com/jquery/3.2.1/jquery.min.js}"></script>-->
    <script type="text/javascript" th:src="@{/js/jquery-3.2.1.min.js}"></script>
    <!--<script type="text/javascript" th:src="@{https://cdn.bootcss.com/bootstrap/3.3.7/js/bootstrap.min.js}"></script>-->
    <script type="text/javascript" th:src="@{/js/bootstrap/bootstrap.min.js}"></script>
    <script type="text/javascript" th:src="@{/js/materialize.min.js}" ></script>
    <script type="text/javascript" th:src="@{/js/index.bundle.js}" ></script>
    <script type="text/javascript" th:src="@{/js/app.js}"></script>
    <script type="text/javascript" th:src="@{/js/goods/detailGoods.js}" ></script>
    <link rel="stylesheet" th:href="@{/css/materialize-icon.css}" />
    <link rel="stylesheet" th:href="@{/css/detail.css}" />
</head>
<body ng-view="ng-view">
<script th:inline="javascript">
    /*<![CDATA[*/
    var cur_user = /*[[${cur_user}]]*/;
    var commentsList = /*[[${commentsList}]]*/;
    var goodsExtend = /*[[${goodsExtend}]]*/;

    function showLogin() {
        if($("#signup-show").css("display")=='block'){
            $("#signup-show").css("display","none");
        }
        if($("#login-show").css("display")=='none'){
            $("#login-show").css("display","block");
        }else{
            $("#login-show").css("display","none");
        }
    }
    function showSignup() {
        if($("#login-show").css("display")=='block'){
            $("#login-show").css("display","none");
        }
        if($("#signup-show").css("display")=='none'){
            $("#signup-show").css("display","block");
        }else{
            $("#signup-show").css("display","none");
        }
    }
    function ChangeName() {
        if($("#changeName").css("display")=='none'){
            $("#changeName").css("display","block");
        }else{
            $("#changeName").css("display","none");
        }
    }

    $(function(){
        detailGoodsPage.init(cur_user, commentsList, goodsExtend);
    });
    /*]]>*/
</script>
<!--
    时间：2018/01/05 22:02:15
    描述：顶部
-->
<div ng-controller="headerController" class="header stark-components navbar-fixed ng-scope">
    <nav class="white nav1">
        <div class="nav-wrapper">
            <a th:href="@{/goods/homeGoods}" class="logo">
                <!--<em class="em1">南工</em>-->
                <em class="em1">
                    <span style="color: #4b82c3;">南</span>
                    <span style="color: #ffd51f;">理</span>
                    <span style="color: #b02b1a;">工</span>
                </em>
                <em class="em2">跳瘙市场</em>
                <em class="em3">
                    <span style="color: #d80000;">g</span>
                    <span style="color: #48bdff;">d</span>
                    <span style="color: #ea83ee;">u</span>
                    <span style="color: #50dd5a;">f</span>
                    <span style="color: #ee9946;">e</span>
                    .market
                </em>
                <!--<em class="em3">gdufe.market</em>-->
            </a>
            <div class="nav-wrapper search-bar">
                <form ng-submit="search()" class="ng-pristine ng-invalid ng-invalid-required" th:action="@{/goods/search}">
                    <div class="input-field">
                        <input id="search" name="str" th:value="${search}" placeholder="搜点什么吧" style="height: 40px;"
                               class="ng-pristine ng-untouched ng-empty ng-invalid ng-invalid-required"/>
                        <label for="search" class="active">
                            <i ng-click="search()" class="iconfont"></i>
                        </label>
                    </div>
                </form>
            </div>
            <ul class="right" th:if="${cur_user != null}">
                <li class="publish-btn">
                    <button data-position="bottom" class="red lighten-1 waves-effect waves-light btn">
                        <a th:href="@{/goods/publishGoods}">我要发布</a>
                    </button>
                </li>
                <li>
                    <a th:href="@{/user/allGoods}">我发布的商品</a>
                </li>
                <li>
                    <a th:text="${cur_user.username}"></a>
                </li>
                <li class="notification">
                    <i ng-click="showNotificationBox()" class="iconfont"></i>
                    <div ng-show="notification.tagIsShow" class="notification-amount red lighten-1 ng-binding ng-hide">0 </div>
                </li>
                <li class="changemore">
                    <a class="changeMoreVertShow()">
                        <i class="iconfont"></i>
                    </a>
                    <div class="more-vert">
                        <ul class="dropdown-content">
                            <li><a th:href="@{/user/home}">个人中心</a></li>
                            <li><a>消息</a></li>
                            <li><a th:onclick="'ChangeName()'">更改用户名</a></li>
                            <li><a th:href="@{/user/logout}">退出登录</a></li>
                        </ul>
                    </div>
                </li>
            </ul>
            <ul class="right" th:if="${cur_user == null}">
                <li class="publish-btn">
                    <button th:onclick="'showLogin()'" data-position="bottom" data-delay="50"
                            data-tooltip="需要先登录哦！" class="red lighten-1 waves-effect waves-light btn" data-tooltip-id="510d3084-e666-f82f-3655-5eae4304a83a" >
                        我要发布</button>
                </li>
                <li>
                    <a th:onclick="'showLogin()'">登录</a>
                </li>
                <li>
                    <a th:onclick="'showSignup()'">注册</a>
                </li>
            </ul>
        </div>
    </nav>
</div>
<!--
    时间：2018/01/05 23:22:19
    描述：登录
-->
<div ng-controller="loginController" class="ng-scope">
    <div id="login-show" class="login stark-components">
        <div class="publish-box z-depth-4">
            <div class="row">
                <a th:onclick="'showLogin()'">
                    <div class="col s12 title"></div>
                </a>
                <form th:action="@{/user/login}" method="post" commandName="user" role="form">
                    <div class="input-field col s12">
                        <input type="text" name="phone" required="required" pattern="^1[0-9]{10}$" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>手机</label>
                    </div>
                    <div class="input-field col s12">
                        <input type="password" name="password" required="required" class="validate ng-pristine ng-untouched ng-empty ng-invalid ng-invalid-required" />
                        <label>密码</label>
                        <a ng-click="showForget()" class="forget-btn">忘记密码？</a>
                    </div>
                    <button type="submit" class="waves-effect waves-light btn login-btn red lighten-1">
                        <i class="iconfont left"></i>
                        <em>登录</em>
                    </button>
                    <div class="col s12 signup-area">
                        <em>没有账号？赶快</em>
                        <a th:onclick="'showSignup()'" class="signup-btn">注册</a>
                        <em>吧！</em>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--
    时间：2018/01/05 23:26:05
    描述：注册
-->
<div ng-controller="signupController" class="ng-scope">
    <div id="signup-show" class="signup stark-components">
        <div class="publish-box z-depth-4">
            <div class="row">
                <a th:onclick="'showSignup()'">
                    <div class="col s12 title"></div>
                </a>
                <form th:action="@{/user/addUser}" method="post" commandName="user" role="form">
                    <div class="input-field col s12">
                        <input type="text" name="username" required="required" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>昵称</label>
                    </div>
                    <div class="input-field col s12">
                        <input type="text" name="phone" required="required" pattern="^1[0-9]{10}$" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>手机</label>
                    </div>
                    <div class="input-field col s12">
                        <input type="password" name="password" required="required" class="validate ng-pristine ng-untouched ng-empty ng-invalid ng-invalid-required" />
                        <label>密码</label>
                    </div>
                    <div ng-show="checkTelIsShow" class="col s12">
                        <button type="submit" class="waves-effect waves-light btn verify-btn red lighten-1">
                            <i class="iconfont left"></i>
                            <em>点击注册</em>
                        </button>
                    </div>
                    <div ng-show="checkTelIsShow" class="login-area col s12">
                        <em>已有账号？去</em>
                        <a th:onclick="'showLogin()'">登录</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--
    时间：2018/01/05 23:36:04
    描述：更改用户名
-->
<div ng-controller="changeNameController" class="ng-scope">
    <div id="changeName" class="change-name stark-components">
        <div class="publish-box z-depth-4">
            <div class="row">
                <div class="col s12 title">
                    <h1>修改用户名</h1>
                </div>
                <form th:action="@{/user/changeName}" method="post" commandName="user" role="form">
                    <div class="input-field col s12">
                        <input type="text" name="username" required="required" class="validate ng-pristine ng-empty ng-invalid ng-invalid-required ng-valid-pattern ng-touched" />
                        <label>修改用户名</label>
                    </div>
                    <div ng-show="checkTelIsShow" class="col s12">
                        <button class="waves-effect waves-light btn publish-btn red lighten-1">
                            <i class="iconfont left"></i>
                            <em>确认</em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!--
    时间：2018/01/06 09:45:17
    描述：显示商品详情
-->
<div ng-controller="detailBoxController" class="detail-box stark-components z-depth-1 row ng-scope">
    <div class="col s12 path">
        <a th:href="@{'/goods/catelog/'+${catelog.id}}" th:text="${catelog.name}"></a>
        <em>></em>
        <a th:text="${goodsExtend.goods.name}"></a>
    </div>
    <div class="col s6">
        <div class="slider" style="height: 440px;">
            <ul class="slides" style="height: 400px;">
                <img th:src="@{'/upload/'+${goodsExtend.images[0].imgUrl}}" />
            </ul>
            <ul class="indicators">
                <li class="indicator-item"></li>
                <li class="indicator-item"></li>
                <li class="indicator-item"></li>
                <li class="indicator-item"></li>
            </ul>
        </div>
    </div>
    <div class="col s6">
        <h1 class="item-name" th:text="${goodsExtend.goods.name}"></h1>
        <h2 class="item-price" th:text="${goodsExtend.goods.price}"></h2>
        <div class="item-public-info">
            <p class="bargain">可讲价</p>
            <p>
                <i class="iconfont"></i>
                <em class="item-location">南阳理工学院</em>
            </p>
        </div>
        <div class="publisher-info-title">
            <em>卖家信息</em><hr/>
        </div>
        <div class="item-contact" th:if="${cur_user == null}">
            <p class="not-login">
                <a th:onclick="'showLogin()'">登录</a>
                <em>或</em>
                <a th:onclick="'showSignup()'">注册</a>
                <em>后查看联系信息</em>
            </p>
        </div>
        <div class="item-contact" th:if="${cur_user != null}">
            <div>
                <div class="base-blue z-depth-1 attr">
                    <i class="iconfont"></i>
                </div>
                <div class="value" th:text="${seller.username}"></div>
            </div>
            <div>
                <div class="base-blue z-depth-1 attr">
                    <i class="iconfont"></i>
                </div>
                <div class="value" th:text="${seller.phone}"></div>
            </div>
            <div>
                <div class="base-blue z-depth-1 attr">
                    <i class="iconfont"></i>
                </div>
                <div class="value" th:text="${seller.qq}"></div>
            </div>
            <div>
                <div class="base-blue z-depth-1 attr">
                    <i class="iconfont"></i>
                </div>
                <div class="value"></div>
            </div>
        </div>
        <h1 class="item-pub-time" th:text="'发布于'+${goodsExtend.goods.startTime}"></h1>
    </div>
</div>
<div class="detail-box stark-components z-depth-1 row">
    <h1 class="title">商品详情</h1>
    <hr class="hr1" />
    <hr class="hr2" />
    <p class="section" th:text="${goodsExtend.goods.describle}"></p>
    <p class="section"></p>
    <p class="section">
        联系我的时候，请说明是在南工跳瘙市场上看见的哦~
    </p>
</div>
<!--
    TODO::评论目前是静态的
-->
<div class="row detail-area">
    <div class="clo s12">
        <div ng-controller="commentController" class="comment stark-components z-depth-1 ng-scope">
            <h1 class="title">评论</h1>
            <hr class="hr1" />
            <hr class="hr2" />
            <div class="comment-add row" th:if="${cur_user == null}" style="font-size: 18px;">
                <div class="input-field col s12">
                    <a th:onclick="'showLogin()'">登录</a>
                    <em>或</em>
                    <a th:onclick="'showSignup()'">注册</a>
                    <em>后查看评论</em>
                </div>
            </div>
            <div th:if="${cur_user != null}" class="comment-collection" th:each="item,itemStats : ${commentsList}">
                <div class="comment-item ng-scope" th:if="${item.atuserId == 0}">
                    <div class="comment-main-content">
                        <em class="name ng-binding" th:text="${item.user.username}+':'">ggee:</em>
                        <em class="ng-hide">回复</em>
                        <em class="name ng-binding ng-hide">@:</em>
                        <em class="ng-binding" th:text="${item.content}">不错。</em>
                    </div>
                    <div class="comment-function">
                        <em class="time ng-biinding" th:text="${item.createAt}">2018/01/06 10:02:02</em>
                        <a class="reply-or-delete" th:onclick="'detailGoodsPage.deleteCommentsAction('+${item.id}+')'" th:if="${cur_user != null and item.userId == cur_user.id}">删除</a>
                        <a class="reply-or-delete" th:onclick="'detailGoodsPage.replyCommentsModalAction('+${itemStats.index}+')'" th:if="${cur_user != null and item.userId != cur_user.id}">回复</a>
                    </div>
                </div>
                <div class="comment-item ng-scope" th:if="${item.atuserId != 0}">
                    <div class="comment-main-content">
                        <em class="name ng-binding" th:text="${item.user.username}">ggee</em>
                        <em>回复</em>
                        <em class="name ng-binding" th:text="${item.atuser.username}+':'">llwwlql:</em>
                        <em class="ng-binding" th:text="${item.content}">不错。</em>
                    </div>
                    <div class="comment-function">
                        <em class="time ng-biinding" th:text="${item.createAt}">2018/01/06 10:02:02</em>
                        <a class="reply-or-delete" th:onclick="'detailGoodsPage.deleteCommentsAction('+${item.id}+')'" th:if="${cur_user != null and item.userId == cur_user.id}">删除</a>
                        <a class="reply-or-delete" th:onclick="'detailGoodsPage.replyCommentsModalAction('+${itemStats.index}+')'" th:if="${cur_user != null and item.userId != cur_user.id}">回复</a>
                    </div>
                </div>
            </div>
            <div class="comment-add row" th:if="${cur_user != null}">
                <div class="input-field col s12">
                    <i class="iconfont prefix"></i>
                    <input id="commentbox" type="text" class="validate ng-pristine ng-untouched ng-valid ng-empty"/>
                    <label for="commentbox">这里写下评论</label>
                    <button type="submit" id="addCommentsBtn" class="waves-effect wave-light btn comment-submit">确认</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--回复窗口-->
<div id="replyCommentsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">回复</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" role="form" onsubmit="return false;">
                    <input type="hidden" id="replyAtuserId" />
                    <div class="form-group">
                        <div class="col-sm-8">
                            <input id="replyContent" type="text" class="form-control" placeholder="" />
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button data-bb-handler="confirm" type="button" id="confirmReplyCommentsBtn" class="btn btn-success radius">
                    <span><i class="icon-ok"></i></span> 确定
                </button>
                <button data-bb-handler="cancel" type="button" id="cancelReplyCommentsBtn" class="btn btn-danger radius">取消</button>
            </div>
        </div>
    </div>
</div>
</body>
</html>